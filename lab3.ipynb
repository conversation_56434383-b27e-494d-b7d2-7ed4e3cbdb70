{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8f7ac209", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from math import log2"]}, {"cell_type": "code", "execution_count": 2, "id": "c80dad0a", "metadata": {}, "outputs": [], "source": ["def entropy(data):\n", "    target = data.iloc[:, -1]  # The target column is the last column\n", "    counts = target.value_counts()  # Count occurrences of each class\n", "    total = len(target)  # Total number of data points\n", "    ent = 0\n", "    for count in counts:\n", "        prob = count / total  # Probability of each class\n", "        ent += -prob * log2(prob)  # Entropy formula\n", "    return ent"]}, {"cell_type": "code", "execution_count": 3, "id": "7cec587b", "metadata": {}, "outputs": [], "source": ["def information_gain(data, feature):\n", "    total_entropy = entropy(data)  # Entropy before the split\n", "    values = data[feature].unique()  # Unique values of the feature\n", "    weighted_entropy = 0\n", "    for value in values:\n", "        subset = data[data[feature] == value]  # Subset of data for each value\n", "        weighted_entropy += (len(subset) / len(data)) * entropy(subset)\n", "    return total_entropy - weighted_entropy  # Gain = Total Entropy - Weighted Entropy"]}, {"cell_type": "code", "execution_count": 4, "id": "bfc02ffd", "metadata": {}, "outputs": [], "source": ["def id3(data, features):\n", "    # If all target values are the same, return the class\n", "    if len(data.iloc[:, -1].unique()) == 1:\n", "        return data.iloc[0, -1]\n", "\n", "    # If no features are left, return the majority class\n", "    if not features:\n", "        return data.iloc[:, -1].mode()[0]  # Most common class\n", "\n", "    # Find the feature with the highest information gain\n", "    gains = {feature: information_gain(data, feature) for feature in features}\n", "    best_feature = max(gains, key=gains.get)\n", "\n", "    # Create a tree node for the best feature\n", "    tree = {best_feature: {}}\n", "\n", "    # Split the dataset and recursively build the tree for each branch\n", "    for value in data[best_feature].unique():\n", "        subset = data[data[best_feature] == value]\n", "        remaining_features = [f for f in features if f != best_feature]\n", "        tree[best_feature][value] = id3(subset, remaining_features)\n", "    \n", "    return tree\n"]}, {"cell_type": "code", "execution_count": 5, "id": "9e04f222", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Decision Tree:\n", "{'Interview': {'Poor': 'No', 'Average': {'Education': {\"Master's\": 'Yes', \"Bachelor's\": 'No', 'PhD': 'Yes'}}, 'Good': 'Yes'}}\n"]}], "source": ["\n", "# New dataset: Loan Approval prediction\n", "data = {\n", "    \"Education\": [\n", "        \"Bachelor's\", \"Master's\", \"PhD\", \"Bachelor's\", \"Master's\", \"PhD\", \"Bachelor's\",\n", "        \"Master's\", \"PhD\", \"Bachelor's\", \"Master's\", \"PhD\", \"Bachelor's\", \"Master's\"\n", "    ],\n", "    \"Experience\": [\n", "        \"None\", \"Junior\", \"Senior\", \"Mid\", \"Junior\", \"Senior\", \"None\",\n", "        \"Mid\", \"Junior\", \"Senior\", \"Mid\", \"None\", \"Junior\", \"Senior\"\n", "    ],\n", "    \"Skills\": [\n", "        \"Basic\", \"Intermediate\", \"Advanced\", \"Intermediate\", \"Basic\", \"Advanced\", \"Intermediate\",\n", "        \"Advanced\", \"Intermediate\", \"Basic\", \"Advanced\", \"Basic\", \"Intermediate\", \"Advanced\"\n", "    ],\n", "    \"Interview\": [\n", "        \"Poor\", \"Average\", \"Good\", \"Good\", \"Poor\", \"Good\", \"Average\",\n", "        \"Good\", \"Average\", \"Poor\", \"Good\", \"Poor\", \"Average\", \"Good\"\n", "    ],\n", "    \"Hired\": [\n", "        \"No\", \"Yes\", \"Yes\", \"Yes\", \"No\", \"Yes\", \"No\",\n", "        \"Yes\", \"Yes\", \"No\", \"Yes\", \"No\", \"No\", \"Yes\"\n", "    ]\n", "}\n", "\n", "\n", "df = pd.DataFrame(data)  # Convert dictionary to DataFrame\n", "\n", "# Build the decision tree\n", "features = list(df.columns[:-1])  # All columns except the target\n", "decision_tree = id3(df, features)\n", "\n", "# Print the decision tree\n", "print(\"Decision Tree:\")\n", "print(decision_tree)\n"]}, {"cell_type": "code", "execution_count": null, "id": "b74f07af", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}