import pandas as pd
from math import log2

def entropy(data):
    target = data.iloc[:, -1]  # The target column is the last column
    counts = target.value_counts()  # Count occurrences of each class
    total = len(target)  # Total number of data points
    ent = 0
    for count in counts:
        prob = count / total  # Probability of each class
        ent += -prob * log2(prob)  # Entropy formula
    return ent

def information_gain(data, feature):
    total_entropy = entropy(data)  # Entropy before the split
    values = data[feature].unique()  # Unique values of the feature
    weighted_entropy = 0
    for value in values:
        subset = data[data[feature] == value]  # Subset of data for each value
        weighted_entropy += (len(subset) / len(data)) * entropy(subset)
    return total_entropy - weighted_entropy  # Gain = Total Entropy - Weighted Entropy

def id3(data, features):
    # If all target values are the same, return the class
    if len(data.iloc[:, -1].unique()) == 1:
        return data.iloc[0, -1]

    # If no features are left, return the majority class
    if not features:
        return data.iloc[:, -1].mode()[0]  # Most common class

    # Find the feature with the highest information gain
    gains = {feature: information_gain(data, feature) for feature in features}
    best_feature = max(gains, key=gains.get)

    # Create a tree node for the best feature
    tree = {best_feature: {}}

    # Split the dataset and recursively build the tree for each branch
    for value in data[best_feature].unique():
        subset = data[data[best_feature] == value]
        remaining_features = [f for f in features if f != best_feature]
        tree[best_feature][value] = id3(subset, remaining_features)
    
    return tree



# dataset: Loan Approval prediction
data = {
    "Education": [
        "Bachelor's", "Master's", "PhD", "Bachelor's", "Master's", "PhD", "Bachelor's",
        "Master's", "PhD", "Bachelor's", "Master's", "PhD", "Bachelor's", "Master's"
    ],
    "Experience": [
        "None", "Junior", "Senior", "Mid", "Junior", "Senior", "None",
        "Mid", "Junior", "Senior", "Mid", "None", "Junior", "Senior"
    ],
    "Skills": [
        "Basic", "Intermediate", "Advanced", "Intermediate", "Basic", "Advanced", "Intermediate",
        "Advanced", "Intermediate", "Basic", "Advanced", "Basic", "Intermediate", "Advanced"
    ],
    "Interview": [
        "Poor", "Average", "Good", "Good", "Poor", "Good", "Average",
        "Good", "Average", "Poor", "Good", "Poor", "Average", "Good"
    ],
    "Hired": [
        "No", "Yes", "Yes", "Yes", "No", "Yes", "No",
        "Yes", "Yes", "No", "Yes", "No", "No", "Yes"
    ]
}


df = pd.DataFrame(data)  # Convert dictionary to DataFrame

# Build the decision tree
features = list(df.columns[:-1])  # All columns except the target
decision_tree = id3(df, features)

# Print the decision tree
print("Decision Tree:")
print(decision_tree)


