{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b74d7c67", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn import datasets\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import classification_report, confusion_matrix"]}, {"cell_type": "code", "execution_count": 3, "id": "43f4fde9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of feature data (X): (150, 2)\n", "Shape of target labels (y): (150,)\n"]}], "source": ["# Load the Iris dataset\n", "iris = datasets.load_iris()\n", "X = iris.data[:, [0, 2]]  # Sepal Length (index 0) and Petal Length (index 2)\n", "y = iris.target\n", "# Check the shape of the feature data (X) and target labels (y)\n", "print(\"Shape of feature data (X):\", X.shape)\n", "print(\"Shape of target labels (y):\", y.shape)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "11520e28", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 5 rows of feature data (X):\n", "[[5.1 1.4]\n", " [4.9 1.4]\n", " [4.7 1.3]\n", " [4.6 1.5]\n", " [5.  1.4]]\n", "\n", "First 5 rows of target labels (y):\n", "[0 0 0 0 0]\n", "\n", "last 5 rows of feature data (X):\n", "[[6.7 5.2]\n", " [6.3 5. ]\n", " [6.5 5.2]\n", " [6.2 5.4]\n", " [5.9 5.1]]\n", "\n", "last 5 rows of target labels (y):\n", "[2 2 2 2 2]\n"]}], "source": ["# Show the first 5 rows of X and y\n", "print(\"First 5 rows of feature data (X):\")\n", "print(X[:5])\n", "\n", "print(\"\\nFirst 5 rows of target labels (y):\")\n", "print(y[:5])\n", "# Show the first 5 rows of X and y\n", "print(\"\\nlast 5 rows of feature data (X):\")\n", "print(X[-5:])\n", "\n", "print(\"\\nlast 5 rows of target labels (y):\")\n", "print(y[-5:])"]}, {"cell_type": "code", "execution_count": 5, "id": "0ce5ec91", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Visualize the dataset\n", "plt.scatter(X[:, 0], X[:, 1], c=y, cmap='winter', edgecolors='k', marker='o')\n", "plt.title(\"Iris Dataset (Sepal Length vs Petal Length)\")\n", "plt.xlabel(\"Sepal Length (cm)\")\n", "plt.ylabel(\"Petal Length (cm)\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 9, "id": "e61edfd0", "metadata": {}, "outputs": [], "source": ["# Split the dataset into training and test sets (70% train, 30% test)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42)"]}, {"cell_type": "code", "execution_count": 10, "id": "1d0d8587", "metadata": {}, "outputs": [{"data": {"text/plain": ["SVC(C=1, kernel='linear')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create an SVM classifier with a linear kernel\n", "svm_classifier = SVC(kernel='linear', C=1)\n", "\n", "# Train the SVM model\n", "svm_classifier.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 11, "id": "6bc09b67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       1.00      1.00      1.00        15\n", "           1       1.00      1.00      1.00        11\n", "           2       1.00      1.00      1.00        12\n", "\n", "    accuracy                           1.00        38\n", "   macro avg       1.00      1.00      1.00        38\n", "weighted avg       1.00      1.00      1.00        38\n", "\n", "Confusion Matrix:\n", " [[15  0  0]\n", " [ 0 11  0]\n", " [ 0  0 12]]\n"]}], "source": ["# Predict on the test data\n", "y_pred = svm_classifier.predict(X_test)\n", "\n", "# Classification report and confusion matrix\n", "print(\"Classification Report:\\n\", classification_report(y_test, y_pred))\n", "print(\"Confusion Matrix:\\n\", confusion_matrix(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": 12, "id": "66961596", "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Plotting the decision boundary (for 2D features)\n", "h = .02  # Step size in the mesh grid, reducing this gives finer resolution\n", "x_min, x_max = X_train[:, 0].min() - 0.5, X_train[:, 0].max() + 0.5  # Slightly reduce the range for zoom effect\n", "y_min, y_max = X_train[:, 1].min() - 0.5, X_train[:, 1].max() + 0.5  # Slightly reduce the range for zoom effect\n", "xx, yy = np.meshgrid(np.arange(x_min, x_max, h), np.arange(y_min, y_max, h))\n", "\n", "# Predict on the grid to plot decision boundary\n", "Z = svm_classifier.predict(np.c_[xx.ravel(), yy.ravel()])\n", "Z = Z.reshape(xx.shape)\n", "\n", "# Plot decision boundary and the training data\n", "plt.contourf(xx, yy, Z, alpha=0.8)  # Color map for the decision regions\n", "plt.scatter(X_train[:, 0], X_train[:, 1], c=y_train, marker='o', edgecolors='k', label='Train Data')\n", "plt.scatter(X_test[:, 0], X_test[:, 1], c=y_test, marker='^', edgecolors='k', label='Test Data')\n", "plt.title(\"SVM Decision Boundary (Sepal Length vs Petal Length)\")\n", "plt.xlabel(\"Sepal Length (cm)\")\n", "plt.ylabel(\"Petal Length (cm)\")\n", "plt.legend()\n", "\n", "# Zooming effect by adjusting axis limits\n", "plt.xlim(x_min, x_max)\n", "plt.ylim(y_min, y_max)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e20b0e4e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}